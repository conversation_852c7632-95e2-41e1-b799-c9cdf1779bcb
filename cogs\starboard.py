import discord
import typing
from discord.ext import commands
from collections import defaultdict

from utilities import checks
from utilities import helpers
from utilities import converters
from utilities import decorators


async def setup(bot):
    await bot.add_cog(Starboard(bot))


class Starboard(commands.Cog):
    """
    Showcase the best messages in your server
    """

    def __init__(self, bot):
        self.bot = bot
        self.starboard_cache = defaultdict(dict)  # Cache for starboard configs
        self.ignored_cache = defaultdict(set)  # Cache for ignored entities
        bot.loop.create_task(self.load_starboard_configs())

    async def load_starboard_configs(self):
        """Load starboard configurations from database"""
        if not self.bot.cxn:
            print("[Starboard] No database connection available")
            return
        try:
            # Load configs
            query = """
                    SELECT * FROM starboard_config;
                    """
            records = await self.bot.cxn.fetch(query)
            print(f"[Starboard] Loaded {len(records)} starboard configs")
            for record in records:
                self.starboard_cache[record['server_id']] = dict(record)

            # Load ignored entities
            query = """
                    SELECT server_id, entity_id, entity_type
                    FROM starboard_ignored;
                    """
            records = await self.bot.cxn.fetch(query)
            print(f"[Starboard] Loaded {len(records)} ignored entities")
            for record in records:
                self.ignored_cache[record['server_id']].add(
                    (record['entity_id'], record['entity_type'])
                )
        except Exception as e:
            print(f"Failed to load starboard configs: {e}")
            import traceback
            traceback.print_exc()

    async def get_starboard_config(self, guild_id):
        """Get starboard config for a guild"""
        if guild_id not in self.starboard_cache:
            query = """
                    SELECT * FROM starboard_config
                    WHERE server_id = $1;
                    """
            record = await self.bot.cxn.fetchrow(query, guild_id)
            if record:
                self.starboard_cache[guild_id] = dict(record)
                print(f"[Starboard] Loaded config for guild {guild_id}: {dict(record)}")
            else:
                print(f"[Starboard] No config found for guild {guild_id}")
                return None
        return self.starboard_cache.get(guild_id)

    async def create_starboard_config(self, guild_id):
        """Create default starboard config"""
        query = """
                INSERT INTO starboard_config (server_id)
                VALUES ($1)
                ON CONFLICT (server_id) DO NOTHING;
                """
        await self.bot.cxn.execute(query, guild_id)
        self.starboard_cache[guild_id] = {
            'server_id': guild_id,
            'channel_id': None,
            'emoji': '⭐',
            'threshold': 3,
            'color': '#323339',
            'locked': False,
            'allow_selfstar': False,
            'show_attachments': True,
            'show_jumpurl': True,
            'show_timestamp': True
        }

    def is_ignored(self, guild_id, entity_id, entity_type):
        """Check if an entity is ignored"""
        return (entity_id, entity_type) in self.ignored_cache.get(guild_id, set())

    @decorators.group(
        invoke_without_command=True,
        brief="Showcase the best messages in your server",
        implemented="2024-07-20 00:00:00.000000",
        updated="2024-07-20 00:00:00.000000",
    )
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def starboard(self, ctx):
        """
        Usage: {0}starboard
        Permission: Manage Guild
        Output:
            Shows the current starboard configuration
            for this server.
        """
        config = await self.get_starboard_config(ctx.guild.id)
        if not config:
            return await ctx.fail("Starboard is not configured for this server. Use `starboard set` to configure it.")

        embed = discord.Embed(
            title="Starboard Configuration",
            color=int(config['color'].replace('#', ''), 16) if config['color'] else 0x323339
        )

        channel = ctx.guild.get_channel(config['channel_id']) if config['channel_id'] else None
        embed.add_field(
            name="Channel",
            value=channel.mention if channel else "Not set",
            inline=True
        )
        embed.add_field(
            name="Emoji",
            value=config['emoji'],
            inline=True
        )
        embed.add_field(
            name="Threshold",
            value=str(config['threshold']),
            inline=True
        )
        embed.add_field(
            name="Status",
            value="Locked" if config['locked'] else "Unlocked",
            inline=True
        )
        embed.add_field(
            name="Self Star",
            value="Enabled" if config['allow_selfstar'] else "Disabled",
            inline=True
        )
        embed.add_field(
            name="Color",
            value=config['color'],
            inline=True
        )

        features = []
        if config['show_attachments']:
            features.append("Attachments")
        if config['show_jumpurl']:
            features.append("Jump URL")
        if config['show_timestamp']:
            features.append("Timestamp")

        embed.add_field(
            name="Features",
            value=", ".join(features) if features else "None",
            inline=False
        )

        await ctx.send(embed=embed)

    @starboard.command(name="reset", brief="Resets guild's configuration for starboard")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def starboard_reset(self, ctx):
        """
        Usage: {0}starboard reset
        Permission: Manage Guild
        Output:
            Resets the starboard configuration
            for this server.
        """
        if await ctx.confirm("This will reset all starboard settings. Continue?"):
            query = """
                    DELETE FROM starboard_config 
                    WHERE server_id = $1;
                    """
            await self.bot.cxn.execute(query, ctx.guild.id)
            
            query = """
                    DELETE FROM starboard_ignored 
                    WHERE server_id = $1;
                    """
            await self.bot.cxn.execute(query, ctx.guild.id)
            
            # Clear cache
            self.starboard_cache.pop(ctx.guild.id, None)
            self.ignored_cache.pop(ctx.guild.id, None)
            
            await ctx.success("Starboard configuration has been reset.")

    @starboard.command(name="lock", brief="Disables/locks starboard from operating")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def starboard_lock(self, ctx):
        """
        Usage: {0}starboard lock
        Permission: Manage Guild
        Output:
            Locks the starboard, preventing
            new messages from being posted.
        """
        config = await self.get_starboard_config(ctx.guild.id)
        if not config:
            await self.create_starboard_config(ctx.guild.id)
            config = await self.get_starboard_config(ctx.guild.id)

        query = """
                UPDATE starboard_config 
                SET locked = TRUE 
                WHERE server_id = $1;
                """
        await self.bot.cxn.execute(query, ctx.guild.id)
        self.starboard_cache[ctx.guild.id]['locked'] = True
        
        await ctx.success("Starboard has been locked.")

    @starboard.command(name="unlock", brief="Enables/unlocks starboard from operating")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def starboard_unlock(self, ctx):
        """
        Usage: {0}starboard unlock
        Permission: Manage Guild
        Output:
            Unlocks the starboard, allowing
            new messages to be posted.
        """
        config = await self.get_starboard_config(ctx.guild.id)
        if not config:
            await self.create_starboard_config(ctx.guild.id)
            config = await self.get_starboard_config(ctx.guild.id)

        query = """
                UPDATE starboard_config 
                SET locked = FALSE 
                WHERE server_id = $1;
                """
        await self.bot.cxn.execute(query, ctx.guild.id)
        self.starboard_cache[ctx.guild.id]['locked'] = False
        
        await ctx.success("Starboard has been unlocked.")

    @starboard.command(name="set", brief="Sets the channel where starboard messages will be sent to")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def starboard_set(self, ctx, channel: discord.TextChannel):
        """
        Usage: {0}starboard set <channel>
        Permission: Manage Guild
        Output:
            Sets the starboard channel where
            starred messages will be posted.
        """
        config = await self.get_starboard_config(ctx.guild.id)
        if not config:
            await self.create_starboard_config(ctx.guild.id)

        query = """
                UPDATE starboard_config 
                SET channel_id = $2 
                WHERE server_id = $1;
                """
        await self.bot.cxn.execute(query, ctx.guild.id, channel.id)
        
        if ctx.guild.id not in self.starboard_cache:
            self.starboard_cache[ctx.guild.id] = {}
        self.starboard_cache[ctx.guild.id]['channel_id'] = channel.id
        
        await ctx.success(f"Starboard channel has been set to {channel.mention}.")

    @starboard.command(name="emoji", brief="Sets the emoji that triggers the starboard messages")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def starboard_emoji(self, ctx, emoji):
        """
        Usage: {0}starboard emoji <emoji>
        Permission: Manage Guild
        Output:
            Sets the emoji that will trigger
            starboard messages when reacted with.
        """
        config = await self.get_starboard_config(ctx.guild.id)
        if not config:
            await self.create_starboard_config(ctx.guild.id)

        query = """
                UPDATE starboard_config
                SET emoji = $2
                WHERE server_id = $1;
                """
        await self.bot.cxn.execute(query, ctx.guild.id, str(emoji))

        if ctx.guild.id not in self.starboard_cache:
            self.starboard_cache[ctx.guild.id] = {}
        self.starboard_cache[ctx.guild.id]['emoji'] = str(emoji)

        await ctx.success(f"Starboard emoji has been set to {emoji}.")

    @starboard.command(name="threshold", brief="Sets the default amount stars needed to post")
    @checks.guild_only()
    async def starboard_threshold(self, ctx, threshold: int):
        """
        Usage: {0}starboard threshold <threshold>
        Permission: None
        Output:
            Sets the number of star reactions
            needed for a message to appear on starboard.
        """
        if threshold < 1:
            return await ctx.fail("Threshold must be at least 1.")

        config = await self.get_starboard_config(ctx.guild.id)
        if not config:
            await self.create_starboard_config(ctx.guild.id)

        query = """
                UPDATE starboard_config
                SET threshold = $2
                WHERE server_id = $1;
                """
        await self.bot.cxn.execute(query, ctx.guild.id, threshold)

        if ctx.guild.id not in self.starboard_cache:
            self.starboard_cache[ctx.guild.id] = {}
        self.starboard_cache[ctx.guild.id]['threshold'] = threshold

        await ctx.success(f"Starboard threshold has been set to {threshold}.")

    @starboard.command(name="selfstar", brief="Allow an author to star their own message")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def starboard_selfstar(self, ctx, setting: converters.BoolConverter):
        """
        Usage: {0}starboard selfstar <setting>
        Permission: Manage Guild
        Output:
            Enables or disables authors from
            starring their own messages.
        """
        config = await self.get_starboard_config(ctx.guild.id)
        if not config:
            await self.create_starboard_config(ctx.guild.id)

        query = """
                UPDATE starboard_config
                SET allow_selfstar = $2
                WHERE server_id = $1;
                """
        await self.bot.cxn.execute(query, ctx.guild.id, setting)

        if ctx.guild.id not in self.starboard_cache:
            self.starboard_cache[ctx.guild.id] = {}
        self.starboard_cache[ctx.guild.id]['allow_selfstar'] = setting

        status = "enabled" if setting else "disabled"
        await ctx.success(f"Self-starring has been {status}.")

    @starboard.command(name="attachments", brief="Allow attachments to appear on Starboard posts")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def starboard_attachments(self, ctx, setting: converters.BoolConverter):
        """
        Usage: {0}starboard attachments <setting>
        Permission: Manage Guild
        Output:
            Enables or disables attachments
            from appearing on starboard posts.
        """
        config = await self.get_starboard_config(ctx.guild.id)
        if not config:
            await self.create_starboard_config(ctx.guild.id)

        query = """
                UPDATE starboard_config
                SET show_attachments = $2
                WHERE server_id = $1;
                """
        await self.bot.cxn.execute(query, ctx.guild.id, setting)

        if ctx.guild.id not in self.starboard_cache:
            self.starboard_cache[ctx.guild.id] = {}
        self.starboard_cache[ctx.guild.id]['show_attachments'] = setting

        status = "enabled" if setting else "disabled"
        await ctx.success(f"Starboard attachments have been {status}.")

    @starboard.command(name="jumpurl", brief="Allow the jump URL to appear on a Starboard post")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def starboard_jumpurl(self, ctx, setting: converters.BoolConverter):
        """
        Usage: {0}starboard jumpurl <setting>
        Permission: Manage Guild
        Output:
            Enables or disables jump URLs
            from appearing on starboard posts.
        """
        config = await self.get_starboard_config(ctx.guild.id)
        if not config:
            await self.create_starboard_config(ctx.guild.id)

        query = """
                UPDATE starboard_config
                SET show_jumpurl = $2
                WHERE server_id = $1;
                """
        await self.bot.cxn.execute(query, ctx.guild.id, setting)

        if ctx.guild.id not in self.starboard_cache:
            self.starboard_cache[ctx.guild.id] = {}
        self.starboard_cache[ctx.guild.id]['show_jumpurl'] = setting

        status = "enabled" if setting else "disabled"
        await ctx.success(f"Starboard jump URLs have been {status}.")

    @starboard.command(name="timestamp", brief="Allow a timestamp to appear on a Starboard post")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def starboard_timestamp(self, ctx, setting: converters.BoolConverter):
        """
        Usage: {0}starboard timestamp <setting>
        Permission: Manage Guild
        Output:
            Enables or disables timestamps
            from appearing on starboard posts.
        """
        config = await self.get_starboard_config(ctx.guild.id)
        if not config:
            await self.create_starboard_config(ctx.guild.id)

        query = """
                UPDATE starboard_config
                SET show_timestamp = $2
                WHERE server_id = $1;
                """
        await self.bot.cxn.execute(query, ctx.guild.id, setting)

        if ctx.guild.id not in self.starboard_cache:
            self.starboard_cache[ctx.guild.id] = {}
        self.starboard_cache[ctx.guild.id]['show_timestamp'] = setting

        status = "enabled" if setting else "disabled"
        await ctx.success(f"Starboard timestamps have been {status}.")

    @starboard.command(name="color", brief="Set default color for starboard posts")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def starboard_color(self, ctx, *, color):
        """
        Usage: {0}starboard color <color>
        Permission: Manage Guild
        Output:
            Sets the default color for
            starboard embed posts.
        """
        config = await self.get_starboard_config(ctx.guild.id)
        if not config:
            await self.create_starboard_config(ctx.guild.id)

        # Parse color input
        color_int = await self.parse_color(color)
        if color_int is None:
            return await ctx.fail("Invalid color format. Use hex (#FF0000), decimal (16711680), or role mention.")

        color_hex = f"#{color_int:06x}"
        query = """
                UPDATE starboard_config
                SET color = $2
                WHERE server_id = $1;
                """
        await self.bot.cxn.execute(query, ctx.guild.id, color_hex)

        if ctx.guild.id not in self.starboard_cache:
            self.starboard_cache[ctx.guild.id] = {}
        self.starboard_cache[ctx.guild.id]['color'] = color_hex

        await ctx.success(f"Starboard color has been set to {color_hex}.")

    async def parse_color(self, color_input):
        """Parse color input and return integer value"""
        try:
            # Try role conversion first
            role = await commands.RoleConverter().convert(None, color_input)
            return role.color.value
        except:
            pass

        # Parse hex, decimal values
        color_input = str(color_input).strip()

        # Remove common prefixes
        if color_input.startswith('#'):
            color_input = color_input[1:]
        elif color_input.startswith('0x'):
            color_input = color_input[2:]

        try:
            # Try hex conversion
            if all(c in '0123456789abcdefABCDEF' for c in color_input):
                return int(color_input, 16)
        except:
            pass

        try:
            # Try decimal conversion
            color_int = int(color_input)
            if 0 <= color_int <= 0xFFFFFF:
                return color_int
        except:
            pass

        return None

    @starboard.group(name="ignore", invoke_without_command=True, brief="Ignore a channel, members or roles for new stars")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def starboard_ignore_group(self, ctx, entity: typing.Union[discord.TextChannel, discord.Member, discord.Role] = None):
        """
        Usage: {0}starboard ignore [channel or member or role]
        Permission: Manage Guild
        Output:
            Ignores the specified channel, member,
            or role from starboard functionality.
            If no entity is provided, shows help.
        """
        if entity is None:
            return await ctx.send_help(ctx.command)

        entity_type = "channel" if isinstance(entity, discord.TextChannel) else \
                     "user" if isinstance(entity, discord.Member) else "role"

        # Check if already ignored
        if self.is_ignored(ctx.guild.id, entity.id, entity_type):
            return await ctx.fail(f"{entity_type.title()} {entity} is already ignored.")

        query = """
                INSERT INTO starboard_ignored (server_id, entity_id, entity_type)
                VALUES ($1, $2, $3)
                ON CONFLICT (server_id, entity_id, entity_type) DO NOTHING;
                """
        await self.bot.cxn.execute(query, ctx.guild.id, entity.id, entity_type)

        # Update cache
        if ctx.guild.id not in self.ignored_cache:
            self.ignored_cache[ctx.guild.id] = set()
        self.ignored_cache[ctx.guild.id].add((entity.id, entity_type))

        await ctx.success(f"{entity_type.title()} {entity} has been ignored from starboard.")

    @starboard_ignore_group.command(name="list", brief="View ignored roles, members and channels for Starboard")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def starboard_ignore_list(self, ctx):
        """
        Usage: {0}starboard ignore list
        Permission: Manage Guild
        Output:
            Shows all ignored channels, members,
            and roles for starboard.
        """
        query = """
                SELECT entity_id, entity_type
                FROM starboard_ignored
                WHERE server_id = $1;
                """
        records = await self.bot.cxn.fetch(query, ctx.guild.id)

        if not records:
            return await ctx.success("No entities are ignored from starboard.")

        embed = discord.Embed(
            title="Starboard Ignored Entities",
            color=0x323339
        )

        channels = []
        users = []
        roles = []

        for record in records:
            entity_id = record['entity_id']
            entity_type = record['entity_type']

            if entity_type == 'channel':
                channel = ctx.guild.get_channel(entity_id)
                if channel:
                    channels.append(channel.mention)
            elif entity_type == 'user':
                user = ctx.guild.get_member(entity_id)
                if user:
                    users.append(str(user))
            elif entity_type == 'role':
                role = ctx.guild.get_role(entity_id)
                if role:
                    roles.append(role.mention)

        if channels:
            embed.add_field(name="Channels", value="\n".join(channels), inline=False)
        if users:
            embed.add_field(name="Users", value="\n".join(users), inline=False)
        if roles:
            embed.add_field(name="Roles", value="\n".join(roles), inline=False)

        if not any([channels, users, roles]):
            embed.description = "All ignored entities have been removed from the server."

        await ctx.send(embed=embed)

    @starboard.command(name="config", brief="View the settings for starboard in guild")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def starboard_config(self, ctx):
        """
        Usage: {0}starboard config
        Permission: Manage Guild
        Output:
            Shows the current starboard configuration
            for this server in detail.
        """
        # This is the same as the main starboard command
        await self.starboard(ctx)

    @starboard.command(name="test", brief="Test starboard functionality", hidden=True)
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def starboard_test(self, ctx):
        """Test command to debug starboard"""
        config = await self.get_starboard_config(ctx.guild.id)
        if not config:
            return await ctx.fail("No starboard config found")

        await ctx.send(f"Config loaded: {config}")

        # Test emoji comparison
        test_emoji = "⭐"
        await ctx.send(f"Test emoji: '{test_emoji}' vs config emoji: '{config['emoji']}' - Match: {test_emoji == config['emoji']}")

    @commands.Cog.listener()
    @decorators.wait_until_ready()
    async def on_raw_reaction_add(self, payload):
        """Handle star reactions being added"""
        print(f"[Starboard] Reaction added: {payload.emoji} by {payload.user_id} in {payload.guild_id}")
        await self.handle_star_reaction(payload, added=True)

    @commands.Cog.listener()
    @decorators.wait_until_ready()
    async def on_raw_reaction_remove(self, payload):
        """Handle star reactions being removed"""
        await self.handle_star_reaction(payload, added=False)

    async def handle_star_reaction(self, payload, added=True):
        """Process star reactions"""
        if not payload.guild_id:
            return

        # Get config
        config = await self.get_starboard_config(payload.guild_id)
        if not config or not config['channel_id'] or config['locked']:
            print(f"[Starboard] No config or channel not set for guild {payload.guild_id}")
            return

        # Check if this is the starboard emoji
        payload_emoji_str = str(payload.emoji)
        config_emoji_str = config['emoji']
        print(f"[Starboard] Comparing emojis: '{payload_emoji_str}' vs '{config_emoji_str}'")
        print(f"[Starboard] Emoji bytes: {payload_emoji_str.encode()} vs {config_emoji_str.encode()}")

        if payload_emoji_str != config_emoji_str:
            print(f"[Starboard] Emoji mismatch: {payload_emoji_str} != {config_emoji_str}")
            return

        guild = self.bot.get_guild(payload.guild_id)
        if not guild:
            print(f"[Starboard] Guild not found: {payload.guild_id}")
            return

        channel = guild.get_channel(payload.channel_id)
        if not channel:
            print(f"[Starboard] Channel not found: {payload.channel_id}")
            return

        print(f"[Starboard] Processing reaction in channel: {channel.name}")

        # Don't process reactions in the starboard channel itself
        if payload.channel_id == config['channel_id']:
            print(f"[Starboard] Ignoring reaction in starboard channel")
            return

        # Check if channel is ignored
        if self.is_ignored(payload.guild_id, payload.channel_id, 'channel'):
            print(f"[Starboard] Channel {channel.name} is ignored")
            return

        # Check if user is ignored
        if self.is_ignored(payload.guild_id, payload.user_id, 'user'):
            print(f"[Starboard] User {payload.user_id} is ignored")
            return

        try:
            message = await channel.fetch_message(payload.message_id)
            print(f"[Starboard] Found message by {message.author}")
        except discord.NotFound:
            print(f"[Starboard] Message not found: {payload.message_id}")
            return

        # Check if message author is ignored
        if self.is_ignored(payload.guild_id, message.author.id, 'user'):
            print(f"[Starboard] Message author {message.author} is ignored")
            return

        # Check if any of the author's roles are ignored
        if isinstance(message.author, discord.Member):
            for role in message.author.roles:
                if self.is_ignored(payload.guild_id, role.id, 'role'):
                    print(f"[Starboard] Message author has ignored role: {role.name}")
                    return

        # Check self-star setting
        if not config['allow_selfstar'] and payload.user_id == message.author.id:
            print(f"[Starboard] Self-starring not allowed")
            return

        print(f"[Starboard] All checks passed, counting stars...")

        # Count current stars
        star_count = 0
        for reaction in message.reactions:
            if str(reaction.emoji) == config['emoji']:
                star_count = reaction.count
                break

        print(f"[Starboard] Star count: {star_count}, threshold: {config['threshold']}")

        # Handle based on star count and threshold
        if star_count >= config['threshold']:
            print(f"[Starboard] Creating/updating starboard message")
            await self.create_or_update_starboard_message(message, star_count, config)
        else:
            print(f"[Starboard] Removing starboard message (below threshold)")
            await self.remove_starboard_message(message, config)

    async def create_or_update_starboard_message(self, message, star_count, config):
        """Create or update a starboard message"""
        guild = message.guild
        starboard_channel = guild.get_channel(config['channel_id'])
        if not starboard_channel:
            return

        # Check if starboard message already exists
        query = """
                SELECT starboard_message_id FROM starboard_messages
                WHERE server_id = $1 AND original_message_id = $2;
                """
        record = await self.bot.cxn.fetchrow(query, guild.id, message.id)

        embed = await self.create_starboard_embed(message, star_count, config)

        if record and record['starboard_message_id']:
            # Update existing message
            try:
                starboard_message = await starboard_channel.fetch_message(record['starboard_message_id'])
                await starboard_message.edit(embed=embed)

                # Update star count in database
                query = """
                        UPDATE starboard_messages
                        SET star_count = $3
                        WHERE server_id = $1 AND original_message_id = $2;
                        """
                await self.bot.cxn.execute(query, guild.id, message.id, star_count)
            except discord.NotFound:
                # Starboard message was deleted, create new one
                await self.create_new_starboard_message(message, star_count, config, embed, starboard_channel)
        else:
            # Create new starboard message
            await self.create_new_starboard_message(message, star_count, config, embed, starboard_channel)

    async def create_new_starboard_message(self, message, star_count, config, embed, starboard_channel):
        """Create a new starboard message"""
        try:
            starboard_message = await starboard_channel.send(embed=embed)

            # Store in database
            query = """
                    INSERT INTO starboard_messages
                    (server_id, original_message_id, starboard_message_id, channel_id, author_id, star_count)
                    VALUES ($1, $2, $3, $4, $5, $6)
                    ON CONFLICT (server_id, original_message_id)
                    DO UPDATE SET starboard_message_id = $3, star_count = $6;
                    """
            await self.bot.cxn.execute(
                query, message.guild.id, message.id, starboard_message.id,
                message.channel.id, message.author.id, star_count
            )
        except discord.Forbidden:
            pass  # No permissions to send in starboard channel

    async def remove_starboard_message(self, message, config):
        """Remove a starboard message if stars fall below threshold"""
        query = """
                SELECT starboard_message_id FROM starboard_messages
                WHERE server_id = $1 AND original_message_id = $2;
                """
        record = await self.bot.cxn.fetchrow(query, message.guild.id, message.id)

        if record and record['starboard_message_id']:
            starboard_channel = message.guild.get_channel(config['channel_id'])
            if starboard_channel:
                try:
                    starboard_message = await starboard_channel.fetch_message(record['starboard_message_id'])
                    await starboard_message.delete()
                except discord.NotFound:
                    pass  # Message already deleted

            # Remove from database
            query = """
                    DELETE FROM starboard_messages
                    WHERE server_id = $1 AND original_message_id = $2;
                    """
            await self.bot.cxn.execute(query, message.guild.id, message.id)

    async def create_starboard_embed(self, message, star_count, config):
        """Create the starboard embed"""
        embed = discord.Embed(
            color=int(config['color'].replace('#', ''), 16) if config['color'] else 0x323339
        )

        # Set author
        embed.set_author(
            name=f"{message.author.display_name}",
            icon_url=message.author.display_avatar.url
        )

        # Add message content
        content = message.content
        if len(content) > 1024:
            content = content[:1021] + "..."

        if content:
            embed.add_field(name="Message", value=content, inline=False)

        # Add star count and channel info
        star_text = f"{config['emoji']} {star_count}"
        embed.add_field(name="Stars", value=star_text, inline=True)
        embed.add_field(name="Channel", value=message.channel.mention, inline=True)

        # Add jump URL if enabled
        if config['show_jumpurl']:
            embed.add_field(name="Jump to Message", value=f"[Click here]({message.jump_url})", inline=True)

        # Add timestamp if enabled
        if config['show_timestamp']:
            embed.timestamp = message.created_at

        # Add image attachment if enabled and exists
        if config['show_attachments'] and message.attachments:
            for attachment in message.attachments:
                if attachment.content_type and attachment.content_type.startswith('image/'):
                    embed.set_image(url=attachment.url)
                    break

        return embed
