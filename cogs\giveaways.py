import discord
import asyncio
import random
import typing
from datetime import datetime, timedelta, timezone
from discord.ext import commands, tasks
from collections import defaultdict

from utilities import checks
from utilities import helpers
from utilities import converters
from utilities import decorators
from utilities import utils


async def setup(bot):
    await bot.add_cog(Giveaways(bot))


class Giveaways(commands.Cog):
    """
    Start a giveaway quickly and easily
    """

    def __init__(self, bot):
        self.bot = bot
        self.giveaway_cache = {}  # Cache for active giveaways
        self.giveaway_check_task.start()
        bot.loop.create_task(self.load_active_giveaways())

    def cog_unload(self):
        self.giveaway_check_task.cancel()

    async def load_active_giveaways(self):
        """Load active giveaways from database"""
        if not self.bot.cxn:
            return
        try:
            query = """
                    SELECT * FROM giveaways 
                    WHERE ended = FALSE AND cancelled = FALSE;
                    """
            records = await self.bot.cxn.fetch(query)
            for record in records:
                self.giveaway_cache[record['message_id']] = dict(record)
        except Exception as e:
            print(f"Failed to load active giveaways: {e}")

    @tasks.loop(seconds=30)
    async def giveaway_check_task(self):
        """Check for giveaways that need to end"""
        if not self.bot.ready:
            return
        
        current_time = datetime.now(timezone.utc)
        ended_giveaways = []
        
        for message_id, giveaway in self.giveaway_cache.items():
            if giveaway['end_time'] <= current_time:
                await self.end_giveaway(giveaway)
                ended_giveaways.append(message_id)
        
        # Remove ended giveaways from cache
        for message_id in ended_giveaways:
            self.giveaway_cache.pop(message_id, None)

    async def parse_duration(self, duration_str):
        """Parse duration string into timedelta"""
        duration_str = duration_str.lower().replace(' ', '')
        
        # Parse different time units
        total_seconds = 0
        current_number = ''
        
        for char in duration_str:
            if char.isdigit():
                current_number += char
            else:
                if current_number:
                    num = int(current_number)
                    if char in ['s', 'sec', 'second']:
                        total_seconds += num
                    elif char in ['m', 'min', 'minute']:
                        total_seconds += num * 60
                    elif char in ['h', 'hr', 'hour']:
                        total_seconds += num * 3600
                    elif char in ['d', 'day']:
                        total_seconds += num * 86400
                    elif char in ['w', 'week']:
                        total_seconds += num * 604800
                    current_number = ''
        
        if current_number:  # Handle case where string ends with number
            total_seconds += int(current_number) * 60  # Default to minutes
        
        if total_seconds < 60:  # Minimum 1 minute
            raise commands.BadArgument("Giveaway duration must be at least 1 minute.")
        
        if total_seconds > 2592000:  # Maximum 30 days
            raise commands.BadArgument("Giveaway duration cannot exceed 30 days.")
        
        return timedelta(seconds=total_seconds)

    async def create_giveaway_embed(self, giveaway_data):
        """Create the giveaway embed"""
        embed = discord.Embed(
            title=f"Giveaway: {giveaway_data['prize']}",
            color=int(giveaway_data['color'].replace('#', ''), 16) if giveaway_data['color'] else 0x323339,
            timestamp=giveaway_data['end_time']
        )
        
        if giveaway_data.get('description'):
            embed.description = giveaway_data['description']
        
        # Add giveaway info
        embed.add_field(
            name="Winners",
            value=str(giveaway_data['winners']),
            inline=True
        )
        
        embed.add_field(
            name="Ends",
            value=utils.format_dt(giveaway_data['end_time'], 'R'),
            inline=True
        )
        
        # Get host info
        host = self.bot.get_user(giveaway_data['host_id'])
        if host:
            embed.add_field(
                name="Host",
                value=host.mention,
                inline=True
            )
        
        # Add requirements if any
        requirements = []
        if giveaway_data['min_level'] > 0:
            requirements.append(f"Min Level: {giveaway_data['min_level']}")
        if giveaway_data['max_level']:
            requirements.append(f"Max Level: {giveaway_data['max_level']}")
        if giveaway_data['min_account_age'] > 0:
            requirements.append(f"Account Age: {giveaway_data['min_account_age']} days")
        if giveaway_data['min_server_stay'] > 0:
            requirements.append(f"Server Stay: {giveaway_data['min_server_stay']} days")
        
        if requirements:
            embed.add_field(
                name="Requirements",
                value="\n".join(requirements),
                inline=False
            )
        
        # Set image and thumbnail
        if giveaway_data.get('image_url'):
            embed.set_image(url=giveaway_data['image_url'])
        if giveaway_data.get('thumbnail_url'):
            embed.set_thumbnail(url=giveaway_data['thumbnail_url'])
        
        embed.set_footer(text="React with 🎉 to enter!")
        
        return embed

    @decorators.group(
        invoke_without_command=True,
        brief="Start a giveaway quickly and easily",
        implemented="2024-07-20 00:00:00.000000",
        updated="2024-07-20 00:00:00.000000",
    )
    @checks.guild_only()
    @checks.has_perms(manage_channels=True)
    async def giveaways(self, ctx):
        """
        Usage: {0}giveaways
        Permission: Manage Channels
        Output:
            Shows help for giveaway commands or
            starts an interactive giveaway setup.
        """
        await ctx.send_help(ctx.command)

    @giveaways.command(name="start", brief="Start a giveaway with your provided duration, winners and prize description")
    @checks.guild_only()
    @checks.has_perms(manage_channels=True)
    async def giveaways_start(self, ctx, channel: discord.TextChannel, duration: str, winners: int, *, prize: str):
        """
        Usage: {0}giveaways start <channel> <duration> <winners> <prize>
        Permission: Manage Channels
        Output:
            Starts a new giveaway in the specified channel.
        Examples:
            {0}giveaways start #giveaways 1h 1 Discord Nitro
            {0}giveaways start #general 30m 3 Steam Gift Cards
        """
        if winners < 1 or winners > 20:
            return await ctx.fail("Number of winners must be between 1 and 20.")
        
        if len(prize) > 256:
            return await ctx.fail("Prize description must be 256 characters or less.")
        
        try:
            duration_delta = await self.parse_duration(duration)
        except commands.BadArgument as e:
            return await ctx.fail(str(e))
        
        end_time = datetime.now(timezone.utc) + duration_delta
        
        # Create giveaway in database
        query = """
                INSERT INTO giveaways (server_id, channel_id, host_id, prize, winners, end_time)
                VALUES ($1, $2, $3, $4, $5, $6)
                RETURNING id;
                """
        giveaway_id = await self.bot.cxn.fetchval(
            query, ctx.guild.id, channel.id, ctx.author.id, prize, winners, end_time
        )
        
        # Create giveaway data
        giveaway_data = {
            'id': giveaway_id,
            'server_id': ctx.guild.id,
            'channel_id': channel.id,
            'host_id': ctx.author.id,
            'prize': prize,
            'winners': winners,
            'end_time': end_time,
            'color': '#323339',
            'min_level': 0,
            'max_level': None,
            'min_account_age': 0,
            'min_server_stay': 0,
            'required_roles': [],
            'winner_roles': [],
            'additional_hosts': []
        }
        
        # Create and send embed
        embed = await self.create_giveaway_embed(giveaway_data)
        giveaway_message = await channel.send(embed=embed)
        await giveaway_message.add_reaction("🎉")
        
        # Update database with message ID
        query = """
                UPDATE giveaways 
                SET message_id = $2 
                WHERE id = $1;
                """
        await self.bot.cxn.execute(query, giveaway_id, giveaway_message.id)
        
        # Add to cache
        giveaway_data['message_id'] = giveaway_message.id
        self.giveaway_cache[giveaway_message.id] = giveaway_data
        
        await ctx.success(f"Giveaway started in {channel.mention}!")

    @giveaways.command(name="list", brief="List every active giveaway in the server")
    @checks.guild_only()
    async def giveaways_list(self, ctx):
        """
        Usage: {0}giveaways list
        Permission: None
        Output:
            Shows all active giveaways in the server.
        """
        query = """
                SELECT * FROM giveaways 
                WHERE server_id = $1 AND ended = FALSE AND cancelled = FALSE
                ORDER BY end_time ASC;
                """
        records = await self.bot.cxn.fetch(query, ctx.guild.id)
        
        if not records:
            return await ctx.success("No active giveaways in this server.")
        
        embed = discord.Embed(
            title="Active Giveaways",
            color=0x323339
        )
        
        for record in records[:10]:  # Limit to 10 giveaways
            channel = ctx.guild.get_channel(record['channel_id'])
            channel_name = channel.name if channel else "Unknown Channel"
            
            embed.add_field(
                name=record['prize'],
                value=f"Channel: #{channel_name}\n"
                      f"Winners: {record['winners']}\n"
                      f"Ends: {utils.format_dt(record['end_time'], 'R')}",
                inline=True
            )
        
        if len(records) > 10:
            embed.set_footer(text=f"Showing 10 of {len(records)} active giveaways")
        
        await ctx.send(embed=embed)

    async def get_giveaway_from_message_link(self, message_link):
        """Get giveaway data from message link"""
        try:
            # Parse message link
            parts = message_link.split('/')
            if len(parts) < 3:
                return None

            message_id = int(parts[-1])

            # Check cache first
            if message_id in self.giveaway_cache:
                return self.giveaway_cache[message_id]

            # Check database
            query = """
                    SELECT * FROM giveaways
                    WHERE message_id = $1 AND ended = FALSE AND cancelled = FALSE;
                    """
            record = await self.bot.cxn.fetchrow(query, message_id)
            if record:
                giveaway_data = dict(record)
                self.giveaway_cache[message_id] = giveaway_data
                return giveaway_data

            return None
        except (ValueError, IndexError):
            return None

    @giveaways.command(name="end", brief="End an active giveaway early")
    @checks.guild_only()
    @checks.has_perms(manage_channels=True)
    async def giveaways_end(self, ctx, *, message_link: str):
        """
        Usage: {0}giveaways end <message link>
        Permission: Manage Channels
        Output:
            Ends the specified giveaway early and picks winners.
        """
        giveaway = await self.get_giveaway_from_message_link(message_link)
        if not giveaway:
            return await ctx.fail("Giveaway not found or already ended.")

        if giveaway['server_id'] != ctx.guild.id:
            return await ctx.fail("You can only end giveaways in this server.")

        await self.end_giveaway(giveaway)
        self.giveaway_cache.pop(giveaway['message_id'], None)

        await ctx.success("Giveaway ended successfully!")

    @giveaways.command(name="cancel", brief="Delete a giveaway without picking any winners")
    @checks.guild_only()
    @checks.has_perms(manage_channels=True)
    async def giveaways_cancel(self, ctx, *, message_link: str):
        """
        Usage: {0}giveaways cancel <message link>
        Permission: Manage Channels
        Output:
            Cancels the specified giveaway without picking winners.
        """
        giveaway = await self.get_giveaway_from_message_link(message_link)
        if not giveaway:
            return await ctx.fail("Giveaway not found or already ended.")

        if giveaway['server_id'] != ctx.guild.id:
            return await ctx.fail("You can only cancel giveaways in this server.")

        # Update database
        query = """
                UPDATE giveaways
                SET cancelled = TRUE
                WHERE id = $1;
                """
        await self.bot.cxn.execute(query, giveaway['id'])

        # Update message
        channel = self.bot.get_channel(giveaway['channel_id'])
        if channel:
            try:
                message = await channel.fetch_message(giveaway['message_id'])
                embed = discord.Embed(
                    title=f"Giveaway Cancelled: {giveaway['prize']}",
                    description="This giveaway has been cancelled.",
                    color=0x323339
                )
                await message.edit(embed=embed)
                await message.clear_reactions()
            except discord.NotFound:
                pass

        self.giveaway_cache.pop(giveaway['message_id'], None)
        await ctx.success("Giveaway cancelled successfully!")

    @giveaways.command(name="reroll", brief="Reroll a winner for the specified giveaway")
    @checks.guild_only()
    @checks.has_perms(manage_channels=True)
    async def giveaways_reroll(self, ctx, message_link: str, winners: int = 1):
        """
        Usage: {0}giveaways reroll <message link> [winners]
        Permission: Manage Channels
        Output:
            Rerolls winners for the specified ended giveaway.
        """
        if winners < 1 or winners > 20:
            return await ctx.fail("Number of winners must be between 1 and 20.")

        try:
            parts = message_link.split('/')
            message_id = int(parts[-1])
        except (ValueError, IndexError):
            return await ctx.fail("Invalid message link.")

        # Get ended giveaway
        query = """
                SELECT * FROM giveaways
                WHERE message_id = $1 AND ended = TRUE;
                """
        giveaway = await self.bot.cxn.fetchrow(query, message_id)
        if not giveaway:
            return await ctx.fail("Giveaway not found or not ended yet.")

        if giveaway['server_id'] != ctx.guild.id:
            return await ctx.fail("You can only reroll giveaways in this server.")

        # Get entries
        query = """
                SELECT user_id FROM giveaway_entries
                WHERE giveaway_id = $1;
                """
        entries = await self.bot.cxn.fetch(query, giveaway['id'])

        if not entries:
            return await ctx.fail("No entries found for this giveaway.")

        # Pick new winners
        entry_ids = [entry['user_id'] for entry in entries]
        new_winners = random.sample(entry_ids, min(winners, len(entry_ids)))

        # Announce reroll
        channel = self.bot.get_channel(giveaway['channel_id'])
        if channel:
            winner_mentions = []
            for winner_id in new_winners:
                user = self.bot.get_user(winner_id)
                if user:
                    winner_mentions.append(user.mention)

            embed = discord.Embed(
                title=f"Giveaway Reroll: {giveaway['prize']}",
                description=f"New winner(s): {', '.join(winner_mentions)}",
                color=0x323339
            )
            await channel.send(embed=embed)

        await ctx.success(f"Rerolled {len(new_winners)} winner(s)!")

    async def end_giveaway(self, giveaway):
        """End a giveaway and pick winners"""
        # Get entries
        query = """
                SELECT user_id FROM giveaway_entries
                WHERE giveaway_id = $1;
                """
        entries = await self.bot.cxn.fetch(query, giveaway['id'])

        # Update giveaway as ended
        query = """
                UPDATE giveaways
                SET ended = TRUE
                WHERE id = $1;
                """
        await self.bot.cxn.execute(query, giveaway['id'])

        channel = self.bot.get_channel(giveaway['channel_id'])
        if not channel:
            return

        try:
            message = await channel.fetch_message(giveaway['message_id'])
        except discord.NotFound:
            return

        if not entries:
            # No entries
            embed = discord.Embed(
                title=f"Giveaway Ended: {giveaway['prize']}",
                description="No one entered this giveaway.",
                color=0x323339
            )
            await message.edit(embed=embed)
            await message.clear_reactions()
            return

        # Pick winners
        entry_ids = [entry['user_id'] for entry in entries]
        num_winners = min(giveaway['winners'], len(entry_ids))
        winners = random.sample(entry_ids, num_winners)

        # Store winners in database
        for winner_id in winners:
            query = """
                    INSERT INTO giveaway_winners (giveaway_id, user_id)
                    VALUES ($1, $2);
                    """
            await self.bot.cxn.execute(query, giveaway['id'], winner_id)

        # Create winner announcement
        winner_mentions = []
        for winner_id in winners:
            user = self.bot.get_user(winner_id)
            if user:
                winner_mentions.append(user.mention)

        embed = discord.Embed(
            title=f"Giveaway Ended: {giveaway['prize']}",
            description=f"Winner(s): {', '.join(winner_mentions)}",
            color=0x323339
        )

        await message.edit(embed=embed)
        await message.clear_reactions()

        # Send winner announcement
        await channel.send(f"Congratulations {', '.join(winner_mentions)}! You won **{giveaway['prize']}**!")

    @giveaways.group(name="edit", invoke_without_command=True, brief="Edit options and limits for a specific giveaway")
    @checks.guild_only()
    @checks.has_perms(manage_channels=True)
    async def giveaways_edit(self, ctx):
        """
        Usage: {0}giveaways edit
        Permission: Manage Channels
        Output:
            Shows available edit options for giveaways.
        """
        await ctx.send_help(ctx.command)

    @giveaways_edit.command(name="winners", brief="Change the amount of winners for a giveaway")
    @checks.guild_only()
    @checks.has_perms(manage_channels=True)
    async def edit_winners(self, ctx, message_link: str, count: int):
        """
        Usage: {0}giveaways edit winners <message link> <count>
        Permission: Manage Channels
        Output:
            Changes the number of winners for the giveaway.
        """
        if count < 1 or count > 20:
            return await ctx.fail("Number of winners must be between 1 and 20.")

        giveaway = await self.get_giveaway_from_message_link(message_link)
        if not giveaway:
            return await ctx.fail("Giveaway not found or already ended.")

        if giveaway['server_id'] != ctx.guild.id:
            return await ctx.fail("You can only edit giveaways in this server.")

        # Update database
        query = """
                UPDATE giveaways
                SET winners = $2
                WHERE id = $1;
                """
        await self.bot.cxn.execute(query, giveaway['id'], count)

        # Update cache and message
        giveaway['winners'] = count
        await self.update_giveaway_message(giveaway)

        await ctx.success(f"Updated winners to {count}.")

    @giveaways_edit.command(name="prize", brief="Change prize for a giveaway")
    @checks.guild_only()
    @checks.has_perms(manage_channels=True)
    async def edit_prize(self, ctx, message_link: str, *, prize: str):
        """
        Usage: {0}giveaways edit prize <message link> <prize>
        Permission: Manage Channels
        Output:
            Changes the prize for the giveaway.
        """
        if len(prize) > 256:
            return await ctx.fail("Prize description must be 256 characters or less.")

        giveaway = await self.get_giveaway_from_message_link(message_link)
        if not giveaway:
            return await ctx.fail("Giveaway not found or already ended.")

        if giveaway['server_id'] != ctx.guild.id:
            return await ctx.fail("You can only edit giveaways in this server.")

        # Update database
        query = """
                UPDATE giveaways
                SET prize = $2
                WHERE id = $1;
                """
        await self.bot.cxn.execute(query, giveaway['id'], prize)

        # Update cache and message
        giveaway['prize'] = prize
        await self.update_giveaway_message(giveaway)

        await ctx.success("Updated prize successfully.")

    @giveaways_edit.command(name="duration", brief="Change the end date for a giveaway")
    @checks.guild_only()
    @checks.has_perms(manage_channels=True)
    async def edit_duration(self, ctx, message_link: str, *, duration: str):
        """
        Usage: {0}giveaways edit duration <message link> <duration>
        Permission: Manage Channels
        Output:
            Changes the end time for the giveaway.
        """
        giveaway = await self.get_giveaway_from_message_link(message_link)
        if not giveaway:
            return await ctx.fail("Giveaway not found or already ended.")

        if giveaway['server_id'] != ctx.guild.id:
            return await ctx.fail("You can only edit giveaways in this server.")

        try:
            duration_delta = await self.parse_duration(duration)
        except commands.BadArgument as e:
            return await ctx.fail(str(e))

        new_end_time = datetime.now(timezone.utc) + duration_delta

        # Update database
        query = """
                UPDATE giveaways
                SET end_time = $2
                WHERE id = $1;
                """
        await self.bot.cxn.execute(query, giveaway['id'], new_end_time)

        # Update cache and message
        giveaway['end_time'] = new_end_time
        await self.update_giveaway_message(giveaway)

        await ctx.success(f"Updated end time to {utils.format_dt(new_end_time, 'F')}.")

    @giveaways_edit.command(name="description", brief="Change description for a giveaway")
    @checks.guild_only()
    @checks.has_perms(manage_channels=True)
    async def edit_description(self, ctx, message_link: str, *, text: str):
        """
        Usage: {0}giveaways edit description <message link> <text>
        Permission: Manage Channels
        Output:
            Changes the description for the giveaway.
        """
        if len(text) > 1024:
            return await ctx.fail("Description must be 1024 characters or less.")

        giveaway = await self.get_giveaway_from_message_link(message_link)
        if not giveaway:
            return await ctx.fail("Giveaway not found or already ended.")

        if giveaway['server_id'] != ctx.guild.id:
            return await ctx.fail("You can only edit giveaways in this server.")

        # Update database
        query = """
                UPDATE giveaways
                SET description = $2
                WHERE id = $1;
                """
        await self.bot.cxn.execute(query, giveaway['id'], text)

        # Update cache and message
        giveaway['description'] = text
        await self.update_giveaway_message(giveaway)

        await ctx.success("Updated description successfully.")

    @giveaways_edit.command(name="color", brief="Change color for a giveaway embed")
    @checks.guild_only()
    @checks.has_perms(manage_channels=True)
    async def edit_color(self, ctx, message_link: str, *, color: str):
        """
        Usage: {0}giveaways edit color <message link> <color>
        Permission: Manage Channels
        Output:
            Changes the embed color for the giveaway.
        """
        giveaway = await self.get_giveaway_from_message_link(message_link)
        if not giveaway:
            return await ctx.fail("Giveaway not found or already ended.")

        if giveaway['server_id'] != ctx.guild.id:
            return await ctx.fail("You can only edit giveaways in this server.")

        # Parse color
        color_int = await self.parse_color(color)
        if color_int is None:
            return await ctx.fail("Invalid color format. Use hex (#FF0000), decimal (16711680), or role mention.")

        color_hex = f"#{color_int:06x}"

        # Update database
        query = """
                UPDATE giveaways
                SET color = $2
                WHERE id = $1;
                """
        await self.bot.cxn.execute(query, giveaway['id'], color_hex)

        # Update cache and message
        giveaway['color'] = color_hex
        await self.update_giveaway_message(giveaway)

        await ctx.success(f"Updated color to {color_hex}.")

    async def parse_color(self, color_input):
        """Parse color input and return integer value"""
        try:
            # Try role conversion first
            role = await commands.RoleConverter().convert(None, color_input)
            return role.color.value
        except:
            pass

        # Parse hex, decimal values
        color_input = str(color_input).strip()

        # Remove common prefixes
        if color_input.startswith('#'):
            color_input = color_input[1:]
        elif color_input.startswith('0x'):
            color_input = color_input[2:]

        try:
            # Try hex conversion
            if all(c in '0123456789abcdefABCDEF' for c in color_input):
                return int(color_input, 16)
        except:
            pass

        try:
            # Try decimal conversion
            color_int = int(color_input)
            if 0 <= color_int <= 0xFFFFFF:
                return color_int
        except:
            pass

        return None

    async def update_giveaway_message(self, giveaway):
        """Update the giveaway message with new data"""
        channel = self.bot.get_channel(giveaway['channel_id'])
        if not channel:
            return

        try:
            message = await channel.fetch_message(giveaway['message_id'])
            embed = await self.create_giveaway_embed(giveaway)
            await message.edit(embed=embed)
        except discord.NotFound:
            pass

    @commands.Cog.listener()
    @decorators.wait_until_ready()
    async def on_raw_reaction_add(self, payload):
        """Handle giveaway entries"""
        if payload.user_id == self.bot.user.id:
            return

        if str(payload.emoji) != "🎉":
            return

        if payload.message_id not in self.giveaway_cache:
            return

        giveaway = self.giveaway_cache[payload.message_id]

        # Check if user can enter
        guild = self.bot.get_guild(payload.guild_id)
        if not guild:
            return

        member = guild.get_member(payload.user_id)
        if not member:
            return

        # Check requirements
        if not await self.check_giveaway_requirements(member, giveaway):
            # Remove reaction if requirements not met
            try:
                channel = guild.get_channel(payload.channel_id)
                message = await channel.fetch_message(payload.message_id)
                await message.remove_reaction("🎉", member)
            except:
                pass
            return

        # Add entry to database
        query = """
                INSERT INTO giveaway_entries (giveaway_id, user_id)
                VALUES ($1, $2)
                ON CONFLICT (giveaway_id, user_id) DO NOTHING;
                """
        await self.bot.cxn.execute(query, giveaway['id'], payload.user_id)

    @commands.Cog.listener()
    @decorators.wait_until_ready()
    async def on_raw_reaction_remove(self, payload):
        """Handle giveaway entry removal"""
        if payload.user_id == self.bot.user.id:
            return

        if str(payload.emoji) != "🎉":
            return

        if payload.message_id not in self.giveaway_cache:
            return

        giveaway = self.giveaway_cache[payload.message_id]

        # Remove entry from database
        query = """
                DELETE FROM giveaway_entries
                WHERE giveaway_id = $1 AND user_id = $2;
                """
        await self.bot.cxn.execute(query, giveaway['id'], payload.user_id)

    async def check_giveaway_requirements(self, member, giveaway):
        """Check if member meets giveaway requirements"""
        # Check account age
        if giveaway['min_account_age'] > 0:
            account_age = (datetime.utcnow() - member.created_at).days
            if account_age < giveaway['min_account_age']:
                return False

        # Check server stay
        if giveaway['min_server_stay'] > 0 and member.joined_at:
            server_stay = (datetime.utcnow() - member.joined_at).days
            if server_stay < giveaway['min_server_stay']:
                return False

        # Check required roles
        if giveaway['required_roles']:
            member_role_ids = [role.id for role in member.roles]
            if not any(role_id in member_role_ids for role_id in giveaway['required_roles']):
                return False

        return True

    @commands.Cog.listener()
    @decorators.wait_until_ready()
    async def on_raw_reaction_add(self, payload):
        """Handle giveaway entries"""
        if payload.user_id == self.bot.user.id:
            return

        if str(payload.emoji) != "🎉":
            return

        if payload.message_id not in self.giveaway_cache:
            return

        giveaway = self.giveaway_cache[payload.message_id]

        # Check if user can enter
        guild = self.bot.get_guild(payload.guild_id)
        if not guild:
            return

        member = guild.get_member(payload.user_id)
        if not member:
            return

        # Check requirements
        if not await self.check_giveaway_requirements(member, giveaway):
            # Remove reaction if requirements not met
            try:
                channel = guild.get_channel(payload.channel_id)
                message = await channel.fetch_message(payload.message_id)
                await message.remove_reaction("🎉", member)
            except:
                pass
            return

        # Add entry to database
        query = """
                INSERT INTO giveaway_entries (giveaway_id, user_id)
                VALUES ($1, $2)
                ON CONFLICT (giveaway_id, user_id) DO NOTHING;
                """
        await self.bot.cxn.execute(query, giveaway['id'], payload.user_id)

    @commands.Cog.listener()
    @decorators.wait_until_ready()
    async def on_raw_reaction_remove(self, payload):
        """Handle giveaway entry removal"""
        if payload.user_id == self.bot.user.id:
            return

        if str(payload.emoji) != "🎉":
            return

        if payload.message_id not in self.giveaway_cache:
            return

        giveaway = self.giveaway_cache[payload.message_id]

        # Remove entry from database
        query = """
                DELETE FROM giveaway_entries
                WHERE giveaway_id = $1 AND user_id = $2;
                """
        await self.bot.cxn.execute(query, giveaway['id'], payload.user_id)

    async def check_giveaway_requirements(self, member, giveaway):
        """Check if member meets giveaway requirements"""
        # Check account age
        if giveaway['min_account_age'] > 0:
            account_age = (datetime.utcnow() - member.created_at).days
            if account_age < giveaway['min_account_age']:
                return False

        # Check server stay
        if giveaway['min_server_stay'] > 0 and member.joined_at:
            server_stay = (datetime.utcnow() - member.joined_at).days
            if server_stay < giveaway['min_server_stay']:
                return False

        # Check required roles
        if giveaway['required_roles']:
            member_role_ids = [role.id for role in member.roles]
            if not any(role_id in member_role_ids for role_id in giveaway['required_roles']):
                return False

        return True

    @giveaways_edit.command(name="minlevel", brief="Set the minimum level requirement for giveaway entry")
    @checks.guild_only()
    @checks.has_perms(manage_channels=True)
    async def edit_minlevel(self, ctx, message_link: str, level: int):
        """
        Usage: {0}giveaways edit minlevel <message link> <level>
        Permission: Manage Channels
        Output:
            Sets the minimum level requirement for giveaway entry.
        """
        if level < 0 or level > 1000:
            return await ctx.fail("Level must be between 0 and 1000.")

        giveaway = await self.get_giveaway_from_message_link(message_link)
        if not giveaway:
            return await ctx.fail("Giveaway not found or already ended.")

        if giveaway['server_id'] != ctx.guild.id:
            return await ctx.fail("You can only edit giveaways in this server.")

        # Update database
        query = """
                UPDATE giveaways
                SET min_level = $2
                WHERE id = $1;
                """
        await self.bot.cxn.execute(query, giveaway['id'], level)

        # Update cache and message
        giveaway['min_level'] = level
        await self.update_giveaway_message(giveaway)

        await ctx.success(f"Updated minimum level to {level}.")

    @giveaways_edit.command(name="maxlevel", brief="Set the maximum level requirement for giveaway entry")
    @checks.guild_only()
    @checks.has_perms(manage_channels=True)
    async def edit_maxlevel(self, ctx, message_link: str, level: int):
        """
        Usage: {0}giveaways edit maxlevel <message link> <level>
        Permission: Manage Channels
        Output:
            Sets the maximum level requirement for giveaway entry.
        """
        if level < 0 or level > 1000:
            return await ctx.fail("Level must be between 0 and 1000.")

        giveaway = await self.get_giveaway_from_message_link(message_link)
        if not giveaway:
            return await ctx.fail("Giveaway not found or already ended.")

        if giveaway['server_id'] != ctx.guild.id:
            return await ctx.fail("You can only edit giveaways in this server.")

        # Update database
        query = """
                UPDATE giveaways
                SET max_level = $2
                WHERE id = $1;
                """
        await self.bot.cxn.execute(query, giveaway['id'], level)

        # Update cache and message
        giveaway['max_level'] = level
        await self.update_giveaway_message(giveaway)

        await ctx.success(f"Updated maximum level to {level}.")

    @giveaways_edit.command(name="age", brief="Set minimum account age for new entries")
    @checks.guild_only()
    @checks.has_perms(manage_channels=True)
    async def edit_age(self, ctx, message_link: str, days: int):
        """
        Usage: {0}giveaways edit age <message link> <days>
        Permission: Manage Channels
        Output:
            Sets the minimum account age requirement in days.
        """
        if days < 0 or days > 365:
            return await ctx.fail("Days must be between 0 and 365.")

        giveaway = await self.get_giveaway_from_message_link(message_link)
        if not giveaway:
            return await ctx.fail("Giveaway not found or already ended.")

        if giveaway['server_id'] != ctx.guild.id:
            return await ctx.fail("You can only edit giveaways in this server.")

        # Update database
        query = """
                UPDATE giveaways
                SET min_account_age = $2
                WHERE id = $1;
                """
        await self.bot.cxn.execute(query, giveaway['id'], days)

        # Update cache and message
        giveaway['min_account_age'] = days
        await self.update_giveaway_message(giveaway)

        await ctx.success(f"Updated minimum account age to {days} days.")

    @giveaways_edit.command(name="stay", brief="Set minimum server stay for new entries")
    @checks.guild_only()
    @checks.has_perms(manage_channels=True)
    async def edit_stay(self, ctx, message_link: str, days: int):
        """
        Usage: {0}giveaways edit stay <message link> <days>
        Permission: Manage Channels
        Output:
            Sets the minimum server stay requirement in days.
        """
        if days < 0 or days > 365:
            return await ctx.fail("Days must be between 0 and 365.")

        giveaway = await self.get_giveaway_from_message_link(message_link)
        if not giveaway:
            return await ctx.fail("Giveaway not found or already ended.")

        if giveaway['server_id'] != ctx.guild.id:
            return await ctx.fail("You can only edit giveaways in this server.")

        # Update database
        query = """
                UPDATE giveaways
                SET min_server_stay = $2
                WHERE id = $1;
                """
        await self.bot.cxn.execute(query, giveaway['id'], days)

        # Update cache and message
        giveaway['min_server_stay'] = days
        await self.update_giveaway_message(giveaway)

        await ctx.success(f"Updated minimum server stay to {days} days.")

    @giveaways_edit.command(name="requiredroles", brief="Set required roles for giveaway entry")
    @checks.guild_only()
    @checks.has_perms(manage_channels=True)
    async def edit_requiredroles(self, ctx, message_link: str, *roles: discord.Role):
        """
        Usage: {0}giveaways edit requiredroles <message link> <roles...>
        Permission: Manage Channels
        Output:
            Sets the required roles for giveaway entry.
        """
        giveaway = await self.get_giveaway_from_message_link(message_link)
        if not giveaway:
            return await ctx.fail("Giveaway not found or already ended.")

        if giveaway['server_id'] != ctx.guild.id:
            return await ctx.fail("You can only edit giveaways in this server.")

        role_ids = [role.id for role in roles]

        # Update database
        query = """
                UPDATE giveaways
                SET required_roles = $2
                WHERE id = $1;
                """
        await self.bot.cxn.execute(query, giveaway['id'], role_ids)

        # Update cache and message
        giveaway['required_roles'] = role_ids
        await self.update_giveaway_message(giveaway)

        if roles:
            role_mentions = [role.mention for role in roles]
            await ctx.success(f"Updated required roles to: {', '.join(role_mentions)}")
        else:
            await ctx.success("Removed all required roles.")

    @giveaways_edit.command(name="roles", brief="Award winners specific roles for a giveaway")
    @checks.guild_only()
    @checks.has_perms(manage_channels=True)
    async def edit_roles(self, ctx, message_link: str, *roles: discord.Role):
        """
        Usage: {0}giveaways edit roles <message link> <roles...>
        Permission: Manage Channels
        Output:
            Sets the roles that winners will receive.
        """
        giveaway = await self.get_giveaway_from_message_link(message_link)
        if not giveaway:
            return await ctx.fail("Giveaway not found or already ended.")

        if giveaway['server_id'] != ctx.guild.id:
            return await ctx.fail("You can only edit giveaways in this server.")

        role_ids = [role.id for role in roles]

        # Update database
        query = """
                UPDATE giveaways
                SET winner_roles = $2
                WHERE id = $1;
                """
        await self.bot.cxn.execute(query, giveaway['id'], role_ids)

        # Update cache
        giveaway['winner_roles'] = role_ids

        if roles:
            role_mentions = [role.mention for role in roles]
            await ctx.success(f"Updated winner roles to: {', '.join(role_mentions)}")
        else:
            await ctx.success("Removed all winner roles.")

    @giveaways_edit.command(name="host", brief="Set new hosts for a giveaway")
    @checks.guild_only()
    @checks.has_perms(manage_channels=True)
    async def edit_host(self, ctx, message_link: str, *members: discord.Member):
        """
        Usage: {0}giveaways edit host <message link> <members...>
        Permission: Manage Channels
        Output:
            Sets additional hosts for the giveaway.
        """
        giveaway = await self.get_giveaway_from_message_link(message_link)
        if not giveaway:
            return await ctx.fail("Giveaway not found or already ended.")

        if giveaway['server_id'] != ctx.guild.id:
            return await ctx.fail("You can only edit giveaways in this server.")

        host_ids = [member.id for member in members]

        # Update database
        query = """
                UPDATE giveaways
                SET additional_hosts = $2
                WHERE id = $1;
                """
        await self.bot.cxn.execute(query, giveaway['id'], host_ids)

        # Update cache
        giveaway['additional_hosts'] = host_ids

        if members:
            host_mentions = [member.mention for member in members]
            await ctx.success(f"Updated additional hosts to: {', '.join(host_mentions)}")
        else:
            await ctx.success("Removed all additional hosts.")

    @giveaways_edit.command(name="image", brief="Change image for a giveaway embed")
    @checks.guild_only()
    @checks.has_perms(manage_channels=True, manage_guild=True)
    async def edit_image(self, ctx, message_link: str, *, url: str = None):
        """
        Usage: {0}giveaways edit image <message link> [url]
        Permission: Manage Channels, Manage Guild
        Output:
            Changes the image for the giveaway embed.
        """
        giveaway = await self.get_giveaway_from_message_link(message_link)
        if not giveaway:
            return await ctx.fail("Giveaway not found or already ended.")

        if giveaway['server_id'] != ctx.guild.id:
            return await ctx.fail("You can only edit giveaways in this server.")

        # Check for attachment
        if not url and ctx.message.attachments:
            url = ctx.message.attachments[0].url

        # Update database
        query = """
                UPDATE giveaways
                SET image_url = $2
                WHERE id = $1;
                """
        await self.bot.cxn.execute(query, giveaway['id'], url)

        # Update cache and message
        giveaway['image_url'] = url
        await self.update_giveaway_message(giveaway)

        if url:
            await ctx.success("Updated giveaway image.")
        else:
            await ctx.success("Removed giveaway image.")

    @giveaways_edit.command(name="thumbnail", brief="Change thumbnail for a giveaway embed")
    @checks.guild_only()
    @checks.has_perms(manage_channels=True)
    async def edit_thumbnail(self, ctx, message_link: str, *, url: str = None):
        """
        Usage: {0}giveaways edit thumbnail <message link> [url]
        Permission: Manage Channels
        Output:
            Changes the thumbnail for the giveaway embed.
        """
        giveaway = await self.get_giveaway_from_message_link(message_link)
        if not giveaway:
            return await ctx.fail("Giveaway not found or already ended.")

        if giveaway['server_id'] != ctx.guild.id:
            return await ctx.fail("You can only edit giveaways in this server.")

        # Check for attachment
        if not url and ctx.message.attachments:
            url = ctx.message.attachments[0].url

        # Update database
        query = """
                UPDATE giveaways
                SET thumbnail_url = $2
                WHERE id = $1;
                """
        await self.bot.cxn.execute(query, giveaway['id'], url)

        # Update cache and message
        giveaway['thumbnail_url'] = url
        await self.update_giveaway_message(giveaway)

        if url:
            await ctx.success("Updated giveaway thumbnail.")
        else:
            await ctx.success("Removed giveaway thumbnail.")
